#!/usr/bin/env python3
"""
Test script untuk menguji API disease_info
"""

import sqlite3
import json
from app import app

def test_database_connection():
    """Test koneksi database dan data penyakit"""
    print("=== Testing Database Connection ===")
    try:
        db = sqlite3.connect('database/padiku.db')
        db.row_factory = sqlite3.Row
        cursor = db.cursor()
        
        # Ambil semua penyakit
        cursor.execute('SELECT * FROM diseases')
        diseases = cursor.fetchall()
        
        print(f"Found {len(diseases)} diseases in database:")
        for disease in diseases:
            print(f"  - ID: {disease['id']}, Name: '{disease['name']}'")
        
        db.close()
        return True
    except Exception as e:
        print(f"Database error: {e}")
        return False

def test_api_endpoint():
    """Test API endpoint disease_info"""
    print("\n=== Testing API Endpoint ===")
    
    with app.test_client() as client:
        # Test dengan nama penyakit yang ada
        test_names = ['Tungro', 'Blast', 'Brown Spot', 'Bacterial Leaf Blight', 'Sehat']
        
        for name in test_names:
            print(f"\nTesting with disease name: '{name}'")
            response = client.get(f'/api/disease_info?name={name}')
            
            print(f"  Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"  Success: {data.get('success', False)}")
                
                if data.get('success'):
                    disease = data.get('disease', {})
                    print(f"  Disease Name: {disease.get('name', 'N/A')}")
                    print(f"  Description: {disease.get('description', 'N/A')[:50]}...")
                else:
                    print(f"  Error: {data.get('error', 'Unknown error')}")
            else:
                print(f"  HTTP Error: {response.status_code}")

def test_html2canvas_availability():
    """Test apakah html2canvas library tersedia"""
    print("\n=== Testing html2canvas Library ===")
    
    # Buat test HTML sederhana
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    </head>
    <body>
        <div id="test">Test Content</div>
        <script>
            if (typeof html2canvas !== 'undefined') {
                console.log('html2canvas is available');
            } else {
                console.log('html2canvas is NOT available');
            }
        </script>
    </body>
    </html>
    """
    
    print("HTML test file created. Check browser console for html2canvas availability.")
    
    with open('test_html2canvas.html', 'w') as f:
        f.write(test_html)
    
    print("Test file saved as: test_html2canvas.html")

if __name__ == '__main__':
    print("Starting Disease API Tests...")
    
    # Test database
    if test_database_connection():
        # Test API
        test_api_endpoint()
    
    # Test html2canvas
    test_html2canvas_availability()
    
    print("\n=== Test Complete ===")
