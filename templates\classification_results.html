{% extends "base.html" %}

{% block title %}<PERSON><PERSON> - Sistem Klasifikasi Penyakit Tanaman Padi{% endblock %}

{% block extra_css %}
{% endblock %}

{% block extra_style %}
<style>
.result-card {
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
}
.result-header {
    background-color: #f8f9fa;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
}
.result-body {
    padding: 20px;
}
.result-image {
    width: 100%;
    max-height: 300px;
    object-fit: cover;
    border-radius: 5px;
}
.result-info {
    margin-top: 20px;
}
.result-prediction {
    font-size: 24px;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 10px;
}
.result-confidence {
    font-size: 18px;
    color: #6c757d;
    margin-bottom: 20px;
}
.result-date {
    font-size: 14px;
    color: #6c757d;
}
.disease-info {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 15px;
    margin-top: 20px;
}
.disease-info h5 {
    margin-bottom: 10px;
    color: #343a40;
}
.screenshot-area {
    position: relative;
    padding: 20px;
    border: 2px dashed #28a745;
    border-radius: 10px;
    margin-top: 20px;
}
.screenshot-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
}
.empty-results {
    text-align: center;
    padding: 50px 0;
}
.empty-results i {
    font-size: 60px;
    color: #e9ecef;
    margin-bottom: 20px;
}
.empty-results h4 {
    color: #6c757d;
    margin-bottom: 10px;
}
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
}
.pagination li {
    margin: 0 5px;
}
.pagination a {
    display: block;
    padding: 8px 12px;
    border-radius: 5px;
    text-decoration: none;
    color: #28a745;
    background-color: #fff;
    border: 1px solid #28a745;
}
.pagination a:hover {
    background-color: #e9f7ef;
}
.pagination .active a {
    background-color: #28a745;
    color: #fff;
}
.text-justify {
    text-align: justify;
}
.badge.bg-success {
    font-size: 14px;
    padding: 5px 10px;
}
.table-borderless th {
    width: 40%;
}
.modal-body {
    padding: 0;
}
#detail-content-modal {
    padding: 20px;
}
@media print {
    body * {
        visibility: hidden;
    }
    #detail-content-modal, #detail-content-modal * {
        visibility: visible;
    }
    #detail-content-modal {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
}
</style>
{% endblock %}

{% block content %}
<h2 class="mb-4">Hasil Klasifikasi</h2>

{% if classifications %}
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-list me-2"></i> Daftar Hasil Klasifikasi
            </div>
            <button class="btn btn-success btn-sm" id="saveAllBtn">
                <i class="fas fa-download me-2"></i> Simpan Semua Hasil
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="classifications-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Gambar</th>
                            <th>Prediksi</th>
                            <th>Kepercayaan</th>
                            <th>Tanggal</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for classification in classifications %}
                        <tr id="row-{{ classification.id }}">
                            <td>{{ classification.id }}</td>
                            <td>
                                <img src="{{ url_for('static', filename='uploads/' + classification.image_path) }}"
                                     alt="Gambar Tanaman" class="img-thumbnail" style="width: 80px; height: 80px; object-fit: cover;">
                            </td>
                            <td>{{ classification.prediction }}</td>
                            <td>{{ "%.2f"|format(classification.confidence) }}%</td>
                            <td>{{ classification.created_at }}</td>
                            <td>
                                <button class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#detailModal{{ classification.id }}">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-success btn-sm" onclick="saveClassificationResult({{ classification.id }})">
                                    <i class="fas fa-download"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Detail Modals -->
    {% for classification in classifications %}
    <div class="modal fade" id="detailModal{{ classification.id }}" tabindex="-1" aria-labelledby="detailModalLabel{{ classification.id }}" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detailModalLabel{{ classification.id }}">Detail Klasifikasi #{{ classification.id }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="detail-content-{{ classification.id }}" class="p-0">
                        <div class="text-center mb-3">
                            <img src="{{ url_for('static', filename='images/rice-plant.png') }}" alt="Logo"
                                 onerror="this.src='https://cdn-icons-png.flaticon.com/512/3039/3039008.png'"
                                 style="width: 60px; margin-bottom: 10px;">
                            <h4>Hasil Klasifikasi Penyakit Tanaman Padi</h4>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="text-center">
                                    <img src="{{ url_for('static', filename='uploads/' + classification.image_path) }}"
                                         alt="Gambar Tanaman" class="img-fluid rounded" style="max-height: 300px;">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">Hasil Klasifikasi</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-borderless">
                                            <tr>
                                                <th>ID Klasifikasi:</th>
                                                <td>#{{ classification.id }}</td>
                                            </tr>
                                            <tr>
                                                <th>Prediksi:</th>
                                                <td><span class="badge bg-success">{{ classification.prediction }}</span></td>
                                            </tr>
                                            <tr>
                                                <th>Kepercayaan:</th>
                                                <td>{{ "%.2f"|format(classification.confidence) }}%</td>
                                            </tr>
                                            <tr>
                                                <th>Tanggal:</th>
                                                <td>{{ classification.created_at }}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">Informasi Penyakit</h5>
                            </div>
                            <div class="card-body">
                                <div id="disease-info-{{ classification.id }}">
                                    <div class="text-center">
                                        <div class="spinner-border text-success" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p>Memuat informasi penyakit...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="dropdown d-inline-block me-2">
                        <button class="btn btn-success dropdown-toggle" type="button" id="saveImageDropdown{{ classification.id }}" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-camera me-2"></i> Simpan Gambar
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="saveImageDropdown{{ classification.id }}">
                            <li><a class="dropdown-item" href="#" onclick="takeScreenshot('detail-content-{{ classification.id }}', 'png')">Simpan sebagai PNG</a></li>
                            <li><a class="dropdown-item" href="#" onclick="takeScreenshot('detail-content-{{ classification.id }}', 'jpeg')">Simpan sebagai JPG</a></li>
                        </ul>
                    </div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Pagination -->
    <div class="pagination-container mt-3">
        <ul class="pagination">
            {% if page > 1 %}
            <li><a href="{{ url_for('classification_results', page=page-1) }}"><i class="fas fa-chevron-left"></i></a></li>
            {% endif %}

            {% for p in range(1, total_pages + 1) %}
            <li class="{{ 'active' if p == page else '' }}">
                <a href="{{ url_for('classification_results', page=p) }}">{{ p }}</a>
            </li>
            {% endfor %}

            {% if page < total_pages %}
            <li><a href="{{ url_for('classification_results', page=page+1) }}"><i class="fas fa-chevron-right"></i></a></li>
            {% endif %}
        </ul>
    </div>
{% else %}
    <div class="empty-results">
        <i class="fas fa-search"></i>
        <h4>Belum ada hasil klasifikasi</h4>
        <p>Silakan lakukan klasifikasi penyakit terlebih dahulu.</p>
        <a href="{{ url_for('classification') }}" class="btn btn-success mt-3">
            <i class="fas fa-microscope me-2"></i> Klasifikasi Penyakit
        </a>
    </div>
{% endif %}

<!-- Hidden canvas for screenshot -->
<canvas id="screenshotCanvas" style="display:none;"></canvas>

<!-- Hidden download link for screenshot -->
<a id="downloadLink" style="display:none;"></a>
{% endblock %}

{% block extra_scripts %}
<!-- html2canvas library untuk screenshot -->
<script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
<script>
// Load disease info when modal is opened
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all detail modals
    {% for classification in classifications %}
    var modal{{ classification.id }} = document.getElementById('detailModal{{ classification.id }}');
    if (modal{{ classification.id }}) {
        modal{{ classification.id }}.addEventListener('shown.bs.modal', function() {
            console.log('Modal opened for classification {{ classification.id }}');
            loadDiseaseInfo('{{ classification.prediction }}', 'disease-info-{{ classification.id }}');
        });
    }
    {% endfor %}
});

// Function to load disease info
function loadDiseaseInfo(diseaseName, elementId) {
    console.log('Loading disease info for:', diseaseName, 'into element:', elementId);

    fetch(`/api/disease_info?name=${encodeURIComponent(diseaseName)}`)
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Disease info response:', data);

            var diseaseInfo = document.getElementById(elementId);
            if (!diseaseInfo) {
                console.error('Element not found:', elementId);
                return;
            }

            if (data.success) {
                diseaseInfo.innerHTML = `
                    <div class="row">
                        <div class="col-md-12">
                            <h5 class="text-success mb-3">${data.disease.name}</h5>

                            <div class="mb-3">
                                <h6 class="text-primary"><i class="fas fa-info-circle me-2"></i>Deskripsi</h6>
                                <p class="text-justify">${data.disease.description}</p>
                            </div>

                            <div class="mb-3">
                                <h6 class="text-warning"><i class="fas fa-exclamation-triangle me-2"></i>Gejala</h6>
                                <p class="text-justify">${data.disease.symptoms}</p>
                            </div>

                            <div>
                                <h6 class="text-danger"><i class="fas fa-medkit me-2"></i>Penanganan</h6>
                                <p class="text-justify">${data.disease.treatment}</p>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                diseaseInfo.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Informasi penyakit tidak ditemukan: ${data.error || 'Unknown error'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading disease info:', error);
            var diseaseInfo = document.getElementById(elementId);
            if (diseaseInfo) {
                diseaseInfo.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Terjadi kesalahan saat memuat informasi penyakit: ${error.message}
                    </div>
                `;
            }
        });
}

// Function to take screenshot of a specific element
function takeScreenshot(elementId, format = 'png') {
    console.log('Taking screenshot of element:', elementId, 'format:', format);

    // Check if html2canvas is available
    if (typeof html2canvas === 'undefined') {
        alert('Library html2canvas tidak tersedia. Silakan refresh halaman dan coba lagi.');
        return;
    }

    const element = document.getElementById(elementId);
    if (!element) {
        alert('Element tidak ditemukan: ' + elementId);
        return;
    }

    // Find the button and show loading state
    const classificationId = elementId.split('-').pop();
    const btn = document.querySelector(`#detailModal${classificationId} .dropdown-toggle`);

    if (btn) {
        const originalBtnText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Menyimpan...';
        btn.disabled = true;

        // Restore button after timeout
        setTimeout(() => {
            btn.innerHTML = originalBtnText;
            btn.disabled = false;
        }, 5000);
    }

    // Add watermark
    const watermarkDiv = document.createElement('div');
    watermarkDiv.style.position = 'absolute';
    watermarkDiv.style.bottom = '10px';
    watermarkDiv.style.right = '10px';
    watermarkDiv.style.fontSize = '12px';
    watermarkDiv.style.color = '#666';
    watermarkDiv.style.padding = '5px';
    watermarkDiv.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
    watermarkDiv.style.borderRadius = '3px';
    watermarkDiv.style.zIndex = '1000';
    watermarkDiv.innerHTML = `Sistem Klasifikasi Penyakit Tanaman Padi - ${new Date().toLocaleString()}`;

    // Store original styles
    const originalStyles = {
        position: element.style.position,
        padding: element.style.padding,
        border: element.style.border,
        borderRadius: element.style.borderRadius,
        backgroundColor: element.style.backgroundColor
    };

    // Apply screenshot styles
    element.style.position = 'relative';
    element.style.padding = '20px';
    element.style.border = '1px solid #ddd';
    element.style.borderRadius = '5px';
    element.style.backgroundColor = '#fff';
    element.appendChild(watermarkDiv);

    // Take screenshot
    html2canvas(element, {
        scale: 2,
        logging: false,
        useCORS: true,
        backgroundColor: '#ffffff',
        allowTaint: true,
        letterRendering: true,
        height: element.scrollHeight,
        width: element.scrollWidth
    }).then(canvas => {
        console.log('Screenshot captured successfully');

        // Convert to image
        let mimeType = 'image/png';
        let fileExtension = 'png';
        let quality = 1.0;

        if (format === 'jpeg' || format === 'jpg') {
            mimeType = 'image/jpeg';
            fileExtension = 'jpg';
            quality = 0.92;
        }

        const imageData = canvas.toDataURL(mimeType, quality);

        // Create and trigger download
        const downloadLink = document.createElement('a');
        downloadLink.href = imageData;
        downloadLink.download = `klasifikasi-padi-${classificationId}.${fileExtension}`;
        downloadLink.style.display = 'none';
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);

        // Restore original styles
        Object.keys(originalStyles).forEach(key => {
            element.style[key] = originalStyles[key];
        });

        // Remove watermark
        if (element.contains(watermarkDiv)) {
            element.removeChild(watermarkDiv);
        }

        // Restore button
        if (btn) {
            btn.innerHTML = btn.innerHTML.replace('<i class="fas fa-spinner fa-spin me-2"></i> Menyimpan...', '<i class="fas fa-camera me-2"></i> Simpan Gambar');
            btn.disabled = false;
        }

        console.log('Screenshot saved successfully');

    }).catch(error => {
        console.error('Error taking screenshot:', error);
        alert('Gagal mengambil screenshot: ' + error.message);

        // Restore original styles
        Object.keys(originalStyles).forEach(key => {
            element.style[key] = originalStyles[key];
        });

        // Remove watermark
        if (element.contains(watermarkDiv)) {
            element.removeChild(watermarkDiv);
        }

        // Restore button
        if (btn) {
            btn.innerHTML = btn.innerHTML.replace('<i class="fas fa-spinner fa-spin me-2"></i> Menyimpan...', '<i class="fas fa-camera me-2"></i> Simpan Gambar');
            btn.disabled = false;
        }
    });
}

// Function to save individual classification result as CSV
function saveClassificationResult(id) {
    const row = document.getElementById(`row-${id}`);
    const cells = row.querySelectorAll('td');

    // Get data from the row
    const classificationId = cells[0].textContent;
    const prediction = cells[2].textContent;
    const confidence = cells[3].textContent;
    const date = cells[4].textContent;

    // Create CSV content
    const csvContent = [
        'ID,Prediksi,Kepercayaan,Tanggal',
        `${classificationId},${prediction},${confidence},${date}`
    ].join('\n');

    // Create blob and download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `klasifikasi-padi-${id}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Function to save all classification results as CSV
document.addEventListener('DOMContentLoaded', function() {
    const saveAllBtn = document.getElementById('saveAllBtn');
    if (saveAllBtn) {
        saveAllBtn.addEventListener('click', function() {
            const table = document.getElementById('classifications-table');
            const rows = table.querySelectorAll('tbody tr');

            // Create CSV header
            let csvContent = 'ID,Prediksi,Kepercayaan,Tanggal\n';

            // Add data from each row
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                const classificationId = cells[0].textContent;
                const prediction = cells[2].textContent;
                const confidence = cells[3].textContent;
                const date = cells[4].textContent;

                csvContent += `${classificationId},${prediction},${confidence},${date}\n`;
            });

            // Create blob and download
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.setAttribute('href', url);
            link.setAttribute('download', 'semua-klasifikasi-padi.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });
    }
});
</script>
{% endblock %}
