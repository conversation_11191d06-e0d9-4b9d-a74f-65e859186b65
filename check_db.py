import sqlite3
import os
from datetime import datetime

def check_database():
    # Pastikan database ada
    db_path = 'database/padiku.db'
    if not os.path.exists(db_path):
        print(f"Database tidak ditemukan di {db_path}")
        exit(1)

    # Buat koneksi ke database
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    try:
        print("="*60)
        print("DATABASE PADIKU - OVERVIEW")
        print("="*60)

        # Cek struktur database
        print("\n📋 STRUKTUR DATABASE:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        for table in tables:
            print(f"  • {table['name']}")

        # Cek tabel users
        print("\n👥 DAFTAR PENGGUNA:")
        print("-" * 80)
        cursor.execute("SELECT * FROM users ORDER BY id")
        users = cursor.fetchall()

        if users:
            print(f"{'ID':<3} {'Username':<15} {'Email':<25} {'Role':<8} {'Full Name':<20} {'Created':<12}")
            print("-" * 80)
            for user in users:
                created = user['created_at'][:10] if user['created_at'] else 'N/A'
                full_name = user['full_name'] if user['full_name'] else 'N/A'
                print(f"{user['id']:<3} {user['username']:<15} {user['email']:<25} {user['role']:<8} {full_name:<20} {created:<12}")
        else:
            print("  Tidak ada pengguna")

        # Cek tabel diseases
        print(f"\n🦠 DAFTAR PENYAKIT:")
        print("-" * 60)
        cursor.execute("SELECT * FROM diseases ORDER BY id")
        diseases = cursor.fetchall()

        if diseases:
            for disease in diseases:
                print(f"  {disease['id']}. {disease['name']}")
                if disease['description']:
                    desc = disease['description'][:80] + "..." if len(disease['description']) > 80 else disease['description']
                    print(f"     📝 {desc}")
                print()
        else:
            print("  Tidak ada data penyakit")

        # Cek tabel classifications
        print("\n📊 RIWAYAT KLASIFIKASI:")
        print("-" * 80)
        cursor.execute("""
            SELECT c.*, u.username
            FROM classifications c
            JOIN users u ON c.user_id = u.id
            ORDER BY c.created_at DESC
            LIMIT 10
        """)
        classifications = cursor.fetchall()

        if classifications:
            print(f"{'ID':<3} {'User':<15} {'Prediksi':<20} {'Confidence':<10} {'Tanggal':<12}")
            print("-" * 80)
            for cls in classifications:
                created = cls['created_at'][:10] if cls['created_at'] else 'N/A'
                confidence = f"{cls['confidence']:.1f}%" if cls['confidence'] else 'N/A'
                print(f"{cls['id']:<3} {cls['username']:<15} {cls['prediction']:<20} {confidence:<10} {created:<12}")

            # Statistik klasifikasi
            cursor.execute("SELECT COUNT(*) as total FROM classifications")
            total_classifications = cursor.fetchone()['total']
            print(f"\n📈 Total klasifikasi: {total_classifications}")

            # Klasifikasi per penyakit
            cursor.execute("""
                SELECT prediction, COUNT(*) as count
                FROM classifications
                GROUP BY prediction
                ORDER BY count DESC
            """)
            stats = cursor.fetchall()
            print("\n📊 Statistik per penyakit:")
            for stat in stats:
                print(f"  • {stat['prediction']}: {stat['count']} kali")
        else:
            print("  Belum ada riwayat klasifikasi")

        # Info database file
        if os.path.exists(db_path):
            size = os.path.getsize(db_path)
            size_mb = size / (1024 * 1024)
            modified = datetime.fromtimestamp(os.path.getmtime(db_path))
            print(f"\n💾 INFO FILE DATABASE:")
            print(f"  📁 Path: {db_path}")
            print(f"  📏 Size: {size_mb:.2f} MB ({size:,} bytes)")
            print(f"  📅 Last modified: {modified.strftime('%Y-%m-%d %H:%M:%S')}")

        print("\n" + "="*60)
        print("✅ Database check completed successfully!")

    except Exception as e:
        print(f"❌ Error: {str(e)}")
    finally:
        conn.close()

def show_table_details(table_name):
    """Menampilkan detail struktur tabel"""
    db_path = 'database/padiku.db'
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        print(f"\n📋 STRUKTUR TABEL: {table_name.upper()}")
        print("-" * 50)

        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()

        print(f"{'No':<3} {'Column':<20} {'Type':<15} {'Null':<8} {'Default':<10}")
        print("-" * 50)
        for col in columns:
            null_ok = "YES" if col['notnull'] == 0 else "NO"
            default = col['dflt_value'] if col['dflt_value'] else ""
            print(f"{col['cid']:<3} {col['name']:<20} {col['type']:<15} {null_ok:<8} {str(default):<10}")

        conn.close()

    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        table_name = sys.argv[1]
        show_table_details(table_name)
    else:
        check_database()

    print("\n💡 Tips:")
    print("  • Untuk melihat struktur tabel: python check_db.py users")
    print("  • Untuk melihat struktur tabel: python check_db.py diseases")
    print("  • Untuk melihat struktur tabel: python check_db.py classifications")
