import sqlite3

# Test database
db = sqlite3.connect('database/padiku.db')
db.row_factory = sqlite3.Row
cursor = db.cursor()

# Check diseases
cursor.execute('SELECT name FROM diseases')
diseases = cursor.fetchall()
print("Diseases:", [d['name'] for d in diseases])

# Test specific disease
cursor.execute('SELECT * FROM diseases WHERE name = ?', ('Tungro',))
tungro = cursor.fetchone()
if tungro:
    print("Tungro found:", tungro['name'])
    print("Description:", tungro['description'][:100])
else:
    print("Tungro not found")

db.close()
print("Test complete")
